import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useProjects, useDeleteProject } from '../../hooks/useProjects'
import { Project } from '../../types/project'
import LoadingSpinner from '../../components/ui/LoadingSpinner'
import {
  FolderIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  UsersIcon,
  DocumentTextIcon,
  EllipsisVerticalIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ShareIcon,
  ArchiveBoxIcon,
  Squares2X2Icon,
  ListBulletIcon,
} from '@heroicons/react/24/outline'
import { Menu, Transition, Dialog } from '@headlessui/react'
import { Fragment } from 'react'
import { format } from 'date-fns'

const ProjectsPage: React.FC = () => {
  const { data: projects, isLoading, error } = useProjects()
  const deleteProject = useDeleteProject()
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [typeFilter, setTypeFilter] = useState('ALL')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null)

  // Mock data for demonstration (in real app, this would come from API)
  const mockProjects = [
    {
      id: '1',
      name: 'Summer Campaign 2024',
      description: 'A comprehensive summer marketing campaign across all social platforms',
      type: 'CAMPAIGN',
      status: 'IN_PROGRESS',
      budget: 15000,
      deadline: '2024-07-15',
      createdAt: '2024-05-01',
      createdBy: { firstName: 'Sarah', lastName: 'Johnson' },
      members: ['Sarah Johnson', 'Mike Chen', 'Alex Rivera'],
      contentItems: ['Post 1', 'Post 2', 'Post 3'],
      progress: 65,
      platforms: ['Instagram', 'Facebook', 'Twitter'],
      metrics: { reach: 125000, engagement: 8.5, clicks: 2400 }
    },
    {
      id: '2',
      name: 'Product Launch',
      description: 'Launch campaign for our new product line with influencer partnerships',
      type: 'ONE_TIME',
      status: 'REVIEW',
      budget: 25000,
      deadline: '2024-06-20',
      createdAt: '2024-04-15',
      createdBy: { firstName: 'Mike', lastName: 'Chen' },
      members: ['Mike Chen', 'Lisa Park'],
      contentItems: ['Launch Video', 'Product Photos', 'Press Release'],
      progress: 90,
      platforms: ['Instagram', 'YouTube', 'LinkedIn'],
      metrics: { reach: 89000, engagement: 12.3, clicks: 3200 }
    },
    {
      id: '3',
      name: 'Brand Awareness',
      description: 'Ongoing brand awareness campaign to increase visibility',
      type: 'ONGOING',
      status: 'PLANNING',
      budget: 8000,
      deadline: '2024-08-01',
      createdAt: '2024-05-10',
      createdBy: { firstName: 'Alex', lastName: 'Rivera' },
      members: ['Alex Rivera', 'Sarah Johnson', 'John Doe'],
      contentItems: ['Brand Story', 'Behind the Scenes'],
      progress: 25,
      platforms: ['Facebook', 'Instagram'],
      metrics: { reach: 45000, engagement: 6.8, clicks: 1200 }
    },
  ]

  // Use mock data if no real projects
  const displayProjects = projects && projects.length > 0 ? projects : mockProjects

  // Filter projects based on search and filters
  const filteredProjects = displayProjects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'ALL' || project.status === statusFilter
    const matchesType = typeFilter === 'ALL' || project.type === typeFilter

    return matchesSearch && matchesStatus && matchesType
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'REVIEW':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PLANNING':
        return <ClockIcon className="h-4 w-4" />
      case 'IN_PROGRESS':
        return <ChartBarIcon className="h-4 w-4" />
      case 'REVIEW':
        return <EyeIcon className="h-4 w-4" />
      case 'COMPLETED':
        return <CheckCircleIcon className="h-4 w-4" />
      case 'CANCELLED':
        return <ExclamationTriangleIcon className="h-4 w-4" />
      default:
        return <FolderIcon className="h-4 w-4" />
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'CAMPAIGN':
        return '🎯'
      case 'ONGOING':
        return '🔄'
      case 'ONE_TIME':
        return '⚡'
      default:
        return '📁'
    }
  }

  const handleDeleteProject = async (project: any) => {
    setProjectToDelete(project)
    setShowDeleteModal(true)
  }

  const confirmDelete = async () => {
    if (projectToDelete) {
      try {
        // In real app, call API to delete
        // await deleteProject.mutateAsync(projectToDelete.id)
        console.log('Deleting project:', projectToDelete.name)
        setShowDeleteModal(false)
        setProjectToDelete(null)
      } catch (error) {
        console.error('Failed to delete project:', error)
      }
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Failed to load projects. Please try again.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Projects</h1>
          <p className="text-gray-600 mt-1">Manage your social media campaigns and projects</p>
          <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
            <span>{filteredProjects.length} total projects</span>
            <span>•</span>
            <span>{filteredProjects.filter(p => p.status === 'IN_PROGRESS').length} in progress</span>
            <span>•</span>
            <span>{filteredProjects.filter(p => p.status === 'COMPLETED').length} completed</span>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>New Project</span>
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
        {/* Search */}
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input pl-10 w-full"
          />
        </div>

        {/* Status Filter */}
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="input w-full sm:w-auto"
        >
          <option value="ALL">All Status</option>
          <option value="PLANNING">Planning</option>
          <option value="IN_PROGRESS">In Progress</option>
          <option value="REVIEW">Review</option>
          <option value="COMPLETED">Completed</option>
          <option value="CANCELLED">Cancelled</option>
        </select>

        {/* Type Filter */}
        <select
          value={typeFilter}
          onChange={(e) => setTypeFilter(e.target.value)}
          className="input w-full sm:w-auto"
        >
          <option value="ALL">All Types</option>
          <option value="CAMPAIGN">Campaign</option>
          <option value="ONGOING">Ongoing</option>
          <option value="ONE_TIME">One Time</option>
        </select>

        {/* View Mode Toggle */}
        <div className="flex items-center bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-md transition-colors ${
              viewMode === 'grid' ? 'bg-white shadow-sm text-primary-600' : 'text-gray-500'
            }`}
          >
            <Squares2X2Icon className="h-5 w-5" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-md transition-colors ${
              viewMode === 'list' ? 'bg-white shadow-sm text-primary-600' : 'text-gray-500'
            }`}
          >
            <ListBulletIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {filteredProjects.length === 0 ? (
        <div className="card">
          <div className="card-content text-center py-16">
            <FolderIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              {searchTerm || statusFilter !== 'ALL' || typeFilter !== 'ALL'
                ? 'No projects match your filters'
                : 'No projects yet'
              }
            </h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              {searchTerm || statusFilter !== 'ALL' || typeFilter !== 'ALL'
                ? 'Try adjusting your search terms or filters to find what you\'re looking for.'
                : 'Get started by creating your first social media project and start managing your campaigns.'
              }
            </p>
            {!searchTerm && statusFilter === 'ALL' && typeFilter === 'ALL' && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="btn-primary"
              >
                Create Your First Project
              </button>
            )}
          </div>
        </div>
      ) : viewMode === 'grid' ? (
        {/* Enhanced Grid View */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <div key={project.id} className="card hover:shadow-xl transition-all duration-200 hover:-translate-y-1">
              <div className="card-content">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-3">
                    <div className="text-3xl">{getTypeIcon(project.type)}</div>
                    <div className="min-w-0 flex-1">
                      <h3 className="font-bold text-gray-900 text-lg truncate">{project.name}</h3>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className={`inline-flex items-center px-2.5 py-0.5 text-xs font-medium rounded-full border ${getStatusColor(project.status)}`}>
                          {getStatusIcon(project.status)}
                          <span className="ml-1">{project.status.replace('_', ' ')}</span>
                        </span>
                        <span className="text-xs text-gray-500">{project.type}</span>
                      </div>
                    </div>
                  </div>

                  <Menu as="div" className="relative">
                    <Menu.Button className="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                      <EllipsisVerticalIcon className="h-5 w-5 text-gray-400" />
                    </Menu.Button>
                    <Transition
                      as={Fragment}
                      enter="transition ease-out duration-100"
                      enterFrom="transform opacity-0 scale-95"
                      enterTo="transform opacity-100 scale-100"
                      leave="transition ease-in duration-75"
                      leaveFrom="transform opacity-100 scale-100"
                      leaveTo="transform opacity-0 scale-95"
                    >
                      <Menu.Items className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-lg bg-white py-2 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <Menu.Item>
                          {({ active }) => (
                            <button
                              className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                                active ? 'bg-gray-50 text-gray-900' : 'text-gray-700'
                              }`}
                            >
                              <EyeIcon className="h-4 w-4 mr-3" />
                              View Details
                            </button>
                          )}
                        </Menu.Item>
                        <Menu.Item>
                          {({ active }) => (
                            <button
                              className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                                active ? 'bg-gray-50 text-gray-900' : 'text-gray-700'
                              }`}
                            >
                              <PencilIcon className="h-4 w-4 mr-3" />
                              Edit Project
                            </button>
                          )}
                        </Menu.Item>
                        <Menu.Item>
                          {({ active }) => (
                            <button
                              className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                                active ? 'bg-gray-50 text-gray-900' : 'text-gray-700'
                              }`}
                            >
                              <ShareIcon className="h-4 w-4 mr-3" />
                              Share Project
                            </button>
                          )}
                        </Menu.Item>
                        <Menu.Item>
                          {({ active }) => (
                            <button
                              className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                                active ? 'bg-gray-50 text-gray-900' : 'text-gray-700'
                              }`}
                            >
                              <ArchiveBoxIcon className="h-4 w-4 mr-3" />
                              Archive
                            </button>
                          )}
                        </Menu.Item>
                        <div className="border-t border-gray-100 my-1"></div>
                        <Menu.Item>
                          {({ active }) => (
                            <button
                              onClick={() => handleDeleteProject(project)}
                              className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                                active ? 'bg-red-50 text-red-900' : 'text-red-700'
                              }`}
                            >
                              <TrashIcon className="h-4 w-4 mr-3" />
                              Delete Project
                            </button>
                          )}
                        </Menu.Item>
                      </Menu.Items>
                    </Transition>
                  </Menu>
                </div>

                {/* Description */}
                {project.description && (
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">{project.description}</p>
                )}

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Progress</span>
                    <span className="text-sm text-gray-500">{project.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${project.progress}%` }}
                    />
                  </div>
                </div>

                {/* Platforms */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {project.platforms?.map((platform, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {platform}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Metrics */}
                <div className="grid grid-cols-3 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <p className="text-xs text-gray-500">Reach</p>
                    <p className="text-sm font-semibold text-gray-900">
                      {project.metrics?.reach ? `${(project.metrics.reach / 1000).toFixed(0)}K` : '0'}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-xs text-gray-500">Engagement</p>
                    <p className="text-sm font-semibold text-gray-900">
                      {project.metrics?.engagement ? `${project.metrics.engagement}%` : '0%'}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-xs text-gray-500">Clicks</p>
                    <p className="text-sm font-semibold text-gray-900">
                      {project.metrics?.clicks ? `${(project.metrics.clicks / 1000).toFixed(1)}K` : '0'}
                    </p>
                  </div>
                </div>

                {/* Project Details */}
                <div className="space-y-3">
                  {project.deadline && (
                    <div className="flex items-center text-sm text-gray-500">
                      <CalendarIcon className="h-4 w-4 mr-2 text-gray-400" />
                      <span>Due {format(new Date(project.deadline), 'MMM d, yyyy')}</span>
                    </div>
                  )}

                  {project.budget && (
                    <div className="flex items-center text-sm text-gray-500">
                      <CurrencyDollarIcon className="h-4 w-4 mr-2 text-gray-400" />
                      <span>${project.budget.toLocaleString()}</span>
                    </div>
                  )}

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center">
                      <UsersIcon className="h-4 w-4 mr-1 text-gray-400" />
                      <span>{project.members?.length || 0} members</span>
                    </div>
                    <div className="flex items-center">
                      <DocumentTextIcon className="h-4 w-4 mr-1 text-gray-400" />
                      <span>{project.contentItems?.length || 0} content</span>
                    </div>
                  </div>
                </div>

                {/* Footer */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-primary-700">
                          {project.createdBy.firstName[0]}{project.createdBy.lastName[0]}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {project.createdBy.firstName} {project.createdBy.lastName}
                        </p>
                        <p className="text-xs text-gray-500">Project Owner</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">Created</p>
                      <p className="text-sm font-medium text-gray-900">
                        {format(new Date(project.createdAt), 'MMM d, yyyy')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        /* List View */
        <div className="card">
          <div className="card-content p-0">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Project
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Progress
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Team
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Budget
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Deadline
                    </th>
                    <th className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredProjects.map((project) => (
                    <tr key={project.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-2xl mr-3">{getTypeIcon(project.type)}</div>
                          <div>
                            <div className="text-sm font-medium text-gray-900">{project.name}</div>
                            <div className="text-sm text-gray-500">{project.type}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(project.status)}`}>
                          {getStatusIcon(project.status)}
                          <span className="ml-1">{project.status.replace('_', ' ')}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-primary-600 h-2 rounded-full"
                              style={{ width: `${project.progress}%` }}
                            />
                          </div>
                          <span className="text-sm text-gray-900">{project.progress}%</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex -space-x-1 mr-2">
                            {project.members?.slice(0, 3).map((member, index) => (
                              <div
                                key={index}
                                className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-xs font-medium text-gray-600 border-2 border-white"
                              >
                                {member[0]}
                              </div>
                            ))}
                            {project.members && project.members.length > 3 && (
                              <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium text-gray-500 border-2 border-white">
                                +{project.members.length - 3}
                              </div>
                            )}
                          </div>
                          <span className="text-sm text-gray-500">{project.members?.length || 0}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${project.budget?.toLocaleString() || '0'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {project.deadline ? format(new Date(project.deadline), 'MMM d, yyyy') : 'No deadline'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <Menu as="div" className="relative inline-block text-left">
                          <Menu.Button className="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                            <EllipsisVerticalIcon className="h-5 w-5 text-gray-400" />
                          </Menu.Button>
                          <Transition
                            as={Fragment}
                            enter="transition ease-out duration-100"
                            enterFrom="transform opacity-0 scale-95"
                            enterTo="transform opacity-100 scale-100"
                            leave="transition ease-in duration-75"
                            leaveFrom="transform opacity-100 scale-100"
                            leaveTo="transform opacity-0 scale-95"
                          >
                            <Menu.Items className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-lg bg-white py-2 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                                      active ? 'bg-gray-50 text-gray-900' : 'text-gray-700'
                                    }`}
                                  >
                                    <EyeIcon className="h-4 w-4 mr-3" />
                                    View Details
                                  </button>
                                )}
                              </Menu.Item>
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                                      active ? 'bg-gray-50 text-gray-900' : 'text-gray-700'
                                    }`}
                                  >
                                    <PencilIcon className="h-4 w-4 mr-3" />
                                    Edit Project
                                  </button>
                                )}
                              </Menu.Item>
                              <div className="border-t border-gray-100 my-1"></div>
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => handleDeleteProject(project)}
                                    className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                                      active ? 'bg-red-50 text-red-900' : 'text-red-700'
                                    }`}
                                  >
                                    <TrashIcon className="h-4 w-4 mr-3" />
                                    Delete Project
                                  </button>
                                )}
                              </Menu.Item>
                            </Menu.Items>
                          </Transition>
                        </Menu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <Dialog open={showDeleteModal} onClose={() => setShowDeleteModal(false)} className="relative z-50">
        <div className="fixed inset-0 bg-black/25" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto max-w-sm rounded-lg bg-white p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <Dialog.Title className="text-lg font-medium text-gray-900">
                  Delete Project
                </Dialog.Title>
              </div>
            </div>
            <p className="text-sm text-gray-500 mb-6">
              Are you sure you want to delete "{projectToDelete?.name}"? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="btn-outline flex-1"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="btn-danger flex-1"
              >
                Delete
              </button>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
    </div>
  )
}

export default ProjectsPage
