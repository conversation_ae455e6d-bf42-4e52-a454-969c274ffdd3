import { useState } from 'react'

function App() {
  const [count, setCount] = useState(0)

  console.log('App rendering... count:', count)

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#f9fafb',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <div style={{
        textAlign: 'center',
        padding: '40px',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        maxWidth: '400px',
        width: '100%'
      }}>
        <div style={{
          width: '48px',
          height: '48px',
          backgroundColor: '#2563eb',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          margin: '0 auto 20px',
          color: 'white',
          fontWeight: 'bold',
          fontSize: '18px'
        }}>
          SH
        </div>

        <h1 style={{
          fontSize: '28px',
          fontWeight: 'bold',
          color: '#1f2937',
          marginBottom: '16px',
          margin: '0 0 16px 0'
        }}>
          SocialHub Pro
        </h1>

        <p style={{
          color: '#6b7280',
          marginBottom: '24px',
          fontSize: '16px',
          margin: '0 0 24px 0'
        }}>
          React App is Working! 🎉
        </p>

        <div style={{ marginBottom: '24px' }}>
          <p style={{ marginBottom: '12px', color: '#374151' }}>
            Counter: <strong>{count}</strong>
          </p>
          <button
            onClick={() => setCount(count + 1)}
            style={{
              backgroundColor: '#2563eb',
              color: 'white',
              padding: '10px 20px',
              borderRadius: '6px',
              border: 'none',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              marginRight: '8px'
            }}
          >
            Increment
          </button>
          <button
            onClick={() => setCount(0)}
            style={{
              backgroundColor: '#6b7280',
              color: 'white',
              padding: '10px 20px',
              borderRadius: '6px',
              border: 'none',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            Reset
          </button>
        </div>

        <div style={{
          padding: '16px',
          backgroundColor: '#f3f4f6',
          borderRadius: '6px',
          marginBottom: '20px'
        }}>
          <h3 style={{
            margin: '0 0 8px 0',
            fontSize: '14px',
            fontWeight: '600',
            color: '#374151'
          }}>
            System Status:
          </h3>
          <ul style={{
            margin: 0,
            padding: 0,
            listStyle: 'none',
            fontSize: '12px',
            color: '#6b7280'
          }}>
            <li>✅ React: Working</li>
            <li>✅ Vite: Working</li>
            <li>✅ TypeScript: Working</li>
            <li>✅ State Management: Working</li>
          </ul>
        </div>

        <button
          onClick={() => {
            console.log('Testing console log...')
            alert('JavaScript is working!')
          }}
          style={{
            backgroundColor: '#059669',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '6px',
            border: 'none',
            cursor: 'pointer',
            fontSize: '14px',
            width: '100%'
          }}
        >
          Test JavaScript
        </button>
      </div>
    </div>
  )
}

export default App
